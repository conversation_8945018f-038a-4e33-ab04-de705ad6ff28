// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.

// export const serviceUrl = 'https://gdkwebserver.ad-ins.com/adimobile/demo/esign/services/';
// export const serviceUrl = 'http://gdkwebsvr:7021/adimobile/esign/services/';
// export const serviceUrl = 'http://localhost:7021/adimobile/esign/services/';
// export const serviceUrl = 'https://esignhub.docsol.id/adimobile/esign/services/';
export const serviceUrl = 'http://localhost:8095/services/';
// export const serviceUrl = 'https://mobiledemoserver.ad-ins.com/adimobile/demo/esign/services/';

export const environment = {
  production: false,
  api: {
    auth: 'oauth/token',
    aes<PERSON>ey: 'aesKey',
    login: 'login',
    register: 'register',
    account: {
      profile: 's/account/getUserProfile',
      changePassword: 's/account/changePassword'
    }
  },
  interval: {
    otp: 10
  },
  excludeStatusCode: [10, 14, 20, 30, 8109, 8117, 8118, 8119, 8176, 8177]
};
